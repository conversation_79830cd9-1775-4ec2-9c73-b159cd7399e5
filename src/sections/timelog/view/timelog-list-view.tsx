'use client';

import isEqual from 'lodash/isEqual';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { paths } from 'src/routes/paths';

import { useTranslate } from 'src/locales';
import { useGetStaffHours } from 'src/api/timelog';

import Scrollbar from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  TableNoData,
  getComparator,
  TableSkeleton,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import { IStaffHours, IStaffHoursTableFilters, IStaffHoursTableFilterValue } from 'src/types/timelog';

import TimelogTableRow from '../timelog-table-row';
import TimelogTableToolbar from '../timelog-table-toolbar';

// ----------------------------------------------------------------------
const today = new Date();

const defaultStartDate = new Date(today);
defaultStartDate.setMonth(today.getMonth() - 1);
defaultStartDate.setDate(1);

const defaultEndDate = new Date(today);
defaultEndDate.setMonth(today.getMonth() + 1);
defaultEndDate.setDate(0);

const defaultFilters: IStaffHoursTableFilters = {
  dateRange: 'last_month',
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  users: [],
};

// ----------------------------------------------------------------------

export default function TimelogListView() {
  const { t } = useTranslate();
  const settings = useSettingsContext();

  const table = useTable({
    defaultRowsPerPage: settings.tableRowsPerPage || 10,
    defaultOrderBy: 'name',
    defaultOrder: 'asc'
  });
  const [tableData, setTableData] = useState<IStaffHours[]>([]);
  const [filters, setFilters] = useState(defaultFilters);

  const { staffHours, staffHoursLoading, staffHoursTotalItems } = useGetStaffHours({
    page: table.page + 1,
    limit: table.rowsPerPage,
    sortBy: `${table.orderBy}:${table.order.toUpperCase()}`,
    search: '',
    filter: {
      ...(filters.startDate && { startDate: filters.startDate }),
      ...(filters.endDate && { endDate: filters.endDate }),
      ...(filters.users.length && { users: filters.users }),
    },
  });

  useEffect(() => {
    if (staffHours) {
      setTableData(staffHours);
    }
  }, [staffHours, table.page, staffHoursTotalItems]);


  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const canReset = !isEqual(defaultFilters, filters);

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name: string, value: IStaffHoursTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading={t("Hours")}
        links={[
          { name: t('Dashboard'), href: paths.dashboard.root },
          { name: t('Work Timelogs') },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }} />

      <Card>

        <TimelogTableToolbar
          filters={filters}
          onFilters={handleFilters} />

        <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
          <TableSelectedAction
            dense={table.dense}
            numSelected={table.selected.length}
            rowCount={dataFiltered.length}
            onSelectAllRows={(checked) =>
              table.onSelectAllRows(
                checked,
                dataFiltered.map((row) => row.id)
              )
            }
          />

          <Scrollbar>
            <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
              {!notFound && (
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={[
                    { id: 'name', label: t('Name') },
                    { id: 'scheduled', label: t('Scheduled (fp)') },
                    { id: 'logged', label: t('Logged (fp)') },
                    { id: 'difference', label: t('Difference') },
                    { id: 'jobs', label: t('Jobs') },
                    { id: '', width: 88 },
                  ]}
                  rowCount={dataFiltered.length}
                  numSelected={table.selected.length}
                  onSort={table.onSort}
                  onSelectAllRows={(checked) =>
                    table.onSelectAllRows(
                      checked,
                      dataFiltered.map((row) => row.id)
                    )
                  }
                />
              )}

              <TableBody>
                {dataFiltered
                  .map((row) => (
                    <TimelogTableRow
                      key={row.id}
                      row={row}
                      selected={table.selected.includes(row.id)}
                      onSelectRow={() => table.onSelectRow(row.id)}
                    />
                  ))}

                {staffHoursLoading && (
                  <TableSkeleton rows={10} sx={{ height: 76 }} />
                )}

                {!staffHoursLoading && <TableNoData notFound={notFound} />}

              </TableBody>
            </Table>
          </Scrollbar>
        </TableContainer>

        {!notFound && (
          <TablePaginationCustom
            count={staffHoursTotalItems}
            page={!staffHoursTotalItems || staffHoursTotalItems <= 0 ? 0 : table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              table.onChangeRowsPerPage(event);
              settings.onUpdate('tableRowsPerPage', event.target.value);
            }} />
        )}
      </Card>
    </Container>
  );
}

// ----------------------------------------------------------------------

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: IStaffHours[];
  comparator: (a: any, b: any) => number;
  filters: IStaffHoursTableFilters;
}) {
  // const { startDate, endDate, users } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index] as const);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  return inputData;
}
