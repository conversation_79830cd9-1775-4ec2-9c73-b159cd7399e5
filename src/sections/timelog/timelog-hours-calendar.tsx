import Calendar from '@fullcalendar/react';
import { useState, useEffect } from 'react';
import dayGridPlugin from '@fullcalendar/daygrid';
import ptLocale from '@fullcalendar/core/locales/pt'

import { Box, Stack } from '@mui/system';
import { Dialog, Avatar, Button, Divider, IconButton, Typography, DialogTitle, DialogContent } from '@mui/material';

import { fDate } from 'src/utils/format-time';

import { useTranslate } from 'src/locales';
import { getUserHoursCalendar } from 'src/api/user';
import { getSmallImageUrl } from 'src/config-global';

import Iconify from 'src/components/iconify';

import { IUser } from 'src/types/user';
import { ICalendarEvent } from 'src/types/calendar';

import { useCalendar } from '../calendar/hooks';
import { StyledCalendar } from '../calendar/styles';


type Props = {
  open: boolean;
  onClose: VoidFunction;
  user?: Partial<IUser>;
};

type HoursEvent = Partial<ICalendarEvent> & {
  textColor?: string;
};

const MonthHours = ({ hours }: { hours: number }) => (
  <Box sx={{
    height: 50,
    alignContent: 'center',
    justifyItems: 'center',
    bgcolor: 'warning.main',
    color: 'warning.contrastText',
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      bottom: 0,
      height: '20%',
      width: '100%',
      clipPath: 'polygon(0 100%, 100% 100%, 50% 0)',
      backgroundColor: 'error.main',
      zIndex: 1
    }
  }}>
    <Typography variant='body2'>{hours}h</Typography>
  </Box>
);

const WeekHours = ({ hours }: { hours: number }) => (
  <Box sx={{
    height: 80,
    pr: '8px',
    alignContent: 'center',
    justifyItems: 'flex-end',
    bgcolor: 'warning.main',
    color: 'warning.contrastText',
    backgroundImage: 'linear-gradient(#FFD666 75%, #FFAB00)',
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      height: '100%',
      width: '20%',
      clipPath: 'polygon(0 5%, 0 95%, 100% 50%)',
      backgroundColor: 'error.main',
      zIndex: 1
    }
  }}>
    <Typography variant='body2'>{hours}h</Typography>
  </Box>
);

export default function TimelogHoursCalendar({ user, open, onClose }: Props) {
  const { t } = useTranslate();

  const {
    date,
    calendarRef,
    onDatePrev,
    onDateNext,
    onInitialView
  } = useCalendar('dayGridMonth');

  const [events, setEvents] = useState<HoursEvent[]>([]);

  useEffect(() => {
    const loadCalendar = async () => {
      const calendar = await getUserHoursCalendar(user?.id || '', date);
      setEvents(calendar.data.map((event: HoursEvent) => ({
        ...event,
        textColor: '#FF0000',
      })));
    };

    if (user && open) {
      loadCalendar();
    }

  }, [user, date, open]);


  const renderEventContent = (eventInfo: { event: ICalendarEvent, timeText: string }) => {
    const { event } = eventInfo;
    const hoursEvent = event as HoursEvent;

    return (
      <Box className="fc-event-main-frame">
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between" className="fc-event-title-container">
          <div className="fc-event-title fc-sticky">
            {hoursEvent.title}
          </div>
        </Stack>
      </Box>
    );
  }

  useEffect(() => {
    onInitialView();
  }, [onInitialView]);

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}>
      <DialogTitle>
        <Stack direction="row" columnGap={2} alignItems="center">
          {user?.photo && (
            <Avatar alt={`${user.firstName} ${user.lastName}`} src={getSmallImageUrl(user.photo)} sx={{ my: -2 }} />
          )}
          {`${user?.firstName} ${user?.lastName}`}
          <Divider orientation="vertical" flexItem />
          {t('Hours Calendar')}
        </Stack>
        <IconButton onClick={onClose} sx={{ position: 'absolute', top: 20, right: 12 }}>
          <Iconify icon="mdi:close" />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3, pb: 3, overflow: 'hidden' }}>

        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ textTransform: 'capitalize' }}>
            {fDate(date, 'MMMM yyyy')}
          </Typography>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Button variant="soft" onClick={onDatePrev} startIcon={<Iconify icon="eva:arrow-ios-back-fill" />}>
              {t('Previous')}
            </Button>

            {/* <Divider orientation="vertical" flexItem sx={{ mx: 1 }} /> */}

            <Button variant="soft" onClick={onDateNext} endIcon={<Iconify icon="eva:arrow-ios-forward-fill" />}>
              {t('Next')}
            </Button>
          </Stack>
        </Stack>

        <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
          <StyledCalendar sx={{ '.fc-today-button': { display: { xs: 'none', md: 'inline-block' } } }}>
            <Calendar
              weekends
              rerenderDelay={20}
              ref={calendarRef}
              dayMaxEventRows={1}
              initialDate={date}
              initialView="dayGridMonth"
              events={events}
              eventDisplay="block"
              eventContent={renderEventContent}
              headerToolbar={false}
              // datesSet={(dateInfo) => {
              //   // console.log('Dates set:', dateInfo);
              //   // This will update the date state when calendar navigation occurs
              //   if (calendarRef.current) {
              //     const calendarApi = calendarRef.current.getApi();
              //     const newDate = calendarApi.getDate();

              //     console.log('Dates set:', newDate.getMonth(), newDate.getFullYear(), date.getMonth(), date.getFullYear());
              //     // Only update if the month or year changed to avoid infinite loops
              //     // if (newDate.getMonth() !== date.getMonth() ||
              //     //   newDate.getFullYear() !== date.getFullYear()) {
              //     //   // Use the date navigation functions from useCalendar
              //     //   if (newDate > date) {
              //     //     onDateNext();
              //     //   } else if (newDate < date) {
              //     //     onDatePrev();
              //     //   }
              //     // }
              //   }
              // }}
              eventOverlap={false}
              height="auto"
              plugins={[dayGridPlugin]}
              locale={ptLocale}
              schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
            />
          </StyledCalendar>

          <Stack direction="column" sx={{ width: { xs: 55, md: 65 } }}>

            <MonthHours hours={160} />

            {[40, 42, 40, 38, 40, 42].map((hours, index) => (
              <WeekHours key={index} hours={hours} />
            ))}

          </Stack>

        </Stack>
      </DialogContent>
    </Dialog>
  );
}
